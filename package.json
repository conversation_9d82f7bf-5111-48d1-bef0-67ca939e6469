{"name": "stitch-app", "main": "expo-router/entry", "version": "1.0.0", "lint-staged": {"*.{js,jsx,ts,tsx,json,md}": ["prettier --write"]}, "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "prepare": "husky install"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.6", "@miblanchard/react-native-slider": "^2.6.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.4.1", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@shopify/flash-list": "1.7.6", "@tanstack/react-query": "^5.80.2", "axios": "^1.9.0", "expo": "~53.0.9", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "prettier": "^3.6.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-calendars": "^1.1313.0", "react-native-circular-progress-indicator": "^4.4.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-size-matters": "^0.4.2", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.0", "typescript": "~5.8.3"}, "private": true}