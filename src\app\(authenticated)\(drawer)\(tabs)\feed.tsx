import CameraIcon from '@assets/svgs/camera-icon.svg';
import CommentIcon from '@assets/svgs/comment-icon.svg';
import HashtagIcon from '@assets/svgs/hashtag-icon.svg';
import HeartIcon from '@assets/svgs/heart-icon.svg';
import OptionsIcon from '@assets/svgs/options-icon.svg';
import PhotoIcon from '@assets/svgs/photo-icon.svg';
import PollIcon from '@assets/svgs/poll-icon.svg';
import SaveIcon from '@assets/svgs/save-icon.svg';
import ThoughtBalloonIcon from '@assets/svgs/thought-balloon-icon.svg';
import { Text, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { AnimatedFlashList } from '@components/shared/animated';
import { Assets } from '@constants';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Image, Pressable } from 'react-native';

interface Comment {
    username: string;
    avatar: any;
    timeAgo: string;
    content: string;
    replyCount: number;
}

interface Post {
    username: string;
    avatar: any;
    timeAgo: string;
    likeCount: number;
    commentCount: number;
    postTitle: string;
    postImage?: any;
    postContent: string;
    hashtags?: string[];
    comments: Comment[];
}

const MOCK_POSTS: Post[] = [
    {
        username: 'travel_guru',
        avatar: Assets.placeholder.feedAvatar,
        timeAgo: '10 mins ago',
        likeCount: 120,
        commentCount: 8,
        postTitle: 'Exploring the Alps!',
        postImage: Assets.placeholder.feed,
        postContent:
            'Just finished hiking the beautiful Alps. The view from the top is breathtaking! Highly recommend to all adventure lovers.',
        hashtags: ['#Alps', '#Adventure', '#Travel'],
        comments: [
            {
                username: 'mountain_lover',
                avatar: Assets.placeholder.commentAvatar1,
                timeAgo: '5 mins ago',
                content: 'Wow, looks amazing! How long was the hike?',
                replyCount: 2,
            },
            {
                username: 'nature_fan',
                avatar: Assets.placeholder.commentAvatar2,
                timeAgo: '3 mins ago',
                content: 'The Alps are on my bucket list! Thanks for sharing.',
                replyCount: 0,
            },
        ],
    },
    {
        username: 'foodie_explorer',
        avatar: Assets.placeholder.feedAvatar,
        timeAgo: '1 hour ago',
        likeCount: 89,
        commentCount: 3,
        postTitle: 'Best Pizza in Naples',
        postImage: undefined,
        postContent:
            'Tried the most delicious pizza in Naples today. The crust was perfect and the cheese was so fresh!',
        hashtags: ['#Foodie', '#Pizza', '#Naples'],
        comments: [
            {
                username: 'pizza_lover',
                avatar: Assets.placeholder.commentAvatar3,
                timeAgo: '45 mins ago',
                content: 'I need to try this place! Where exactly is it?',
                replyCount: 1,
            },
        ],
    },
    {
        username: 'wellness_journey',
        avatar: Assets.placeholder.feedAvatar,
        timeAgo: '2 days ago',
        likeCount: 200,
        commentCount: 15,
        postTitle: 'Morning Yoga Routine',
        postImage: Assets.placeholder.feed,
        postContent:
            'Started a new morning yoga routine and it has changed my life. Feeling more energetic and focused every day.',
        hashtags: ['#Yoga', '#Wellness', '#Routine'],
        comments: [
            {
                username: 'yogi_life',
                avatar: Assets.placeholder.commentAvatar1,
                timeAgo: '1 day ago',
                content: 'So inspiring! Can you share your routine?',
                replyCount: 3,
            },
            {
                username: 'fit_mom',
                avatar: Assets.placeholder.commentAvatar2,
                timeAgo: '1 day ago',
                content: 'Love this! I need to get back to yoga.',
                replyCount: 0,
            },
        ],
    },
];

const FeedCard = ({ post, onShowComments }: { post: Post; onShowComments: () => void }) => {
    return (
        <View px={8} pb={24}>
            <View
                bg="white"
                br={20}
                bw={1}
                bc="purpleLight"
            >
                <View px={13} py={16}>
                    <View fd="row" ai="center" mb={16} jc="space-between">
                        <View fd="row" ai="center">
                            <Image
                                width={40}
                                height={40}
                                borderRadius={20}
                                source={post.avatar}
                                style={{ marginRight: 12 }}
                            />
                            <View>
                                <Text fs="16" fw="700">
                                    @{post.username}
                                </Text>
                                <Text fs="12" color="neutral60" mt={4}>
                                    {post.timeAgo}
                                </Text>
                            </View>
                        </View>
                        <OptionsIcon width={6} height={15} />
                    </View>

                    {post.postImage && (
                        <View mb={16}>
                            <Image
                                width={100}
                                height={180}
                                borderRadius={12}
                                source={post.postImage}
                                style={{ backgroundColor: '#eee' }}
                                resizeMode="cover"
                            />
                        </View>
                    )}

                    <Text fw="700" fs="18">
                        {post.postTitle}
                    </Text>
                    <Text mt={8}>{post.postContent}</Text>

                    {post.hashtags && post.hashtags.length > 0 && (
                            <Text color="purple500" fw="600" fs="14">
                                {post.hashtags.join(' ')}
                            </Text>
                    )}

                    <View mt={16} fd="row" ai="center" mb={16} jc="space-between">
                        <View fd="row" ai="center">
                            <HeartIcon width={24} height={24} style={{ marginRight: 6 }} />
                            <Text mr={16} fs="14">
                                {post.likeCount}
                            </Text>
                            <Pressable
                                onPress={onShowComments}
                                style={{ flexDirection: 'row', alignItems: 'center' }}
                            >
                                <CommentIcon width={24} height={24} style={{ marginRight: 6 }} />
                                <Text fs="14">{post.commentCount}</Text>
                            </Pressable>
                        </View>
                        <View>
                            <SaveIcon width={24} height={24} />
                        </View>
                    </View>
                </View>
            </View>
        </View>
    );
};

const FeedTabScreen = () => {
    const bottomSheetRef = useRef<BottomSheet>(null);
    const snapPoints = useMemo(() => ['45%', '85%'], []);
    const [selectedPost, setSelectedPost] = useState<Post | null>(null);

    const handleShowComments = useCallback((post: Post) => {
        setSelectedPost(post);
        setTimeout(() => {
            bottomSheetRef.current?.snapToIndex(1);
        }, 0);
    }, []);

    const handleSheetChanges = useCallback((index: number) => {
        if (index === -1) setSelectedPost(null);
    }, []);

    return (
        <ScreenWrapper>
            <AnimatedFlashList
                ListHeaderComponent={
                    <View
                        px={8}
                        py={16}
                    >
                        <View
                            px={20}
                            py={18}
                            bg="white"
                            br={20}
                            style={{
                                boxShadow: '0px 4px 10px rgba(0,0,0,0.12)',
                                shadowColor: '#000',
                                shadowOffset: { width: 0, height: 4 },
                                shadowOpacity: 0.12,
                                shadowRadius: 10,
                            }}
                        >
                            <View
                                fd="row"
                                ai="center"
                                jc="space-between"
                                mb={16}
                            >
                                <Text
                                    fs="16"
                                    fw="500"
                                    color="neutral60"
                                >
                                    What's on your mind today?
                                </Text>
                                <ThoughtBalloonIcon
                                    width={43}
                                    height={43}
                                />
                            </View>
                            <View
                                fd="row"
                                ai="center"
                            >
                                <PhotoIcon
                                    width={16}
                                    height={16}
                                    style={{ marginRight: 25 }}
                                />
                                <CameraIcon
                                    width={16}
                                    height={16}
                                    style={{ marginRight: 25 }}
                                />
                                <HashtagIcon
                                    width={16}
                                    height={16}
                                    style={{ marginRight: 25 }}
                                />
                                <PollIcon
                                    width={16}
                                    height={16}
                                    style={{ marginRight: 25 }}
                                />
                            </View>
                        </View>
                    </View>
                }
                data={MOCK_POSTS}
                renderItem={({ item }: { item: Post }) => {
                    return <FeedCard post={item} onShowComments={() => handleShowComments(item)} />;
                }}
            />
            <BottomSheet
                ref={bottomSheetRef}
                index={-1}
                snapPoints={snapPoints}
                onChange={handleSheetChanges}
                enablePanDownToClose
                style={{ borderRadius: 24 }}
            >
                <View px={20} py={25} flex={1} jc="flex-end">
                    <Text fs="24" fw="600" mb={24} ta="left" color="neutral80">
                        Comments
                    </Text>
                    <BottomSheetScrollView showsVerticalScrollIndicator={false}>
                        {selectedPost?.comments.map((comment, index) => (
                            <View key={index} mb={18}>
                                <View fd="row" ai="center" mb={0}>
                                    <Image
                                        width={36}
                                        height={36}
                                        borderRadius={18}
                                        source={comment.avatar}
                                    />
                                    <View w={6} />
                                    <Text fw="600" fs="14" color="neutral80">
                                        {comment.username}
                                    </Text>
                                    <View flex={1} />
                                    <Text color="neutral50" fs="10">
                                        {comment.timeAgo}
                                    </Text>
                                </View>
                                <View fd="row" ai="flex-start">
                                    <View w={36} />
                                    <View flex={1}>
                                        <Text fs="10" color="neutral70" mt={2}>
                                            {comment.content}
                                        </Text>
                                        <View h={8} />
                                        <View fd="row" ai="center">
                                            <Text fs="12" color="neutral70" fw="700">
                                                Reply
                                            </Text>
                                            <View w={2} h={2} mx={5} bg="neutral80" br={100} />
                                            <Text fw="400" fs="10" color="neutral50">
                                                Show {comment.replyCount}{' '}
                                                {comment.replyCount === 1 ? 'reply' : 'replies'}
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                            </View>
                        ))}
                    </BottomSheetScrollView>
                </View>
            </BottomSheet>
        </ScreenWrapper>
    );
};

export default FeedTabScreen;
